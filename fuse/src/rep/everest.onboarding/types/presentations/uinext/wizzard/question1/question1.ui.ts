/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation UI Types
// ******************************************************************************

import type {
  GetElementType,
  UIExecutionContext
} from '@everestsystems/content-core';

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

export namespace question1PresentationUI {
  export namespace dataSets {
    export namespace header {
      export type instance = {
        subtitle?: everest_appserver_primitive_Text | undefined | null;
        title?: everest_appserver_primitive_Text | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }

    export namespace migrationOptions {
      export type instance = {
        hasExistingData?: everest_appserver_primitive_TrueFalse | undefined | null;
        hasExistingDataLabel?: everest_appserver_primitive_Text | undefined | null;
        isNewSetup?: everest_appserver_primitive_TrueFalse | undefined | null;
        isNewSetupLabel?: everest_appserver_primitive_Text | undefined | null;
        selectedValue?: everest_appserver_primitive_Text | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }

    export namespace navigationButtons {
      export type instance = {
        backLabel?: everest_appserver_primitive_Text | undefined | null;
        canContinue?: everest_appserver_primitive_TrueFalse | undefined | null;
        canGoBack?: everest_appserver_primitive_TrueFalse | undefined | null;
        continueLabel?: everest_appserver_primitive_Text | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }

    export namespace progress {
      export type instance = {
        currentStep?: everest_appserver_primitive_Number | undefined | null;
        progressPercentage?: everest_appserver_primitive_Decimal | undefined | null;
        stepLabel?: everest_appserver_primitive_Text | undefined | null;
        totalSteps?: everest_appserver_primitive_Number | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }

    export namespace questionContent {
      export type instance = {
        descriptions?: everest_appserver_primitive_Text | undefined | null;
        question?: everest_appserver_primitive_Text | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }
  }

  export namespace actions {
    export namespace continueToNext {
      export type input = never;
      export type output = {
        nextStep: everest_appserver_primitive_Text;
        success: everest_appserver_primitive_TrueFalse;
        validationMessage: everest_appserver_primitive_Text;
      };
    }

    export namespace goBack {
      export type input = never;
      export type output = void;
    }

    export namespace initializeQuestion {
      export type input = never;
      export type output = {
        questionData: everest_appserver_primitive_Text;
      };
    }

    export namespace selectOption {
      export type input = never;
      export type output = {
        success: everest_appserver_primitive_TrueFalse;
      };
    }
  }

  export type context = UIExecutionContext.BasicExecutionContext & {
    data: {
      header: dataSets.header.data;
      migrationOptions: dataSets.migrationOptions.data;
      navigationButtons: dataSets.navigationButtons.data;
      progress: dataSets.progress.data;
      questionContent: dataSets.questionContent.data;
    };
    state: Record<string, unknown>;
    presentationClient: {
      continueToNext: () => Promise<actions.continueToNext.output>;
      goBack: () => Promise<actions.goBack.output>;
      initializeQuestion: () => Promise<actions.initializeQuestion.output>;
      selectOption: () => Promise<actions.selectOption.output>;
    };
  }
}
