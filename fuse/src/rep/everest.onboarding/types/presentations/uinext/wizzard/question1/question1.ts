/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation Types
// ******************************************************************************

import type {
  ActionValidationResult,
  COMPONENT,
  CONFIGURATIONS,
  DATA,
  DynamicFieldsMetadata,
  GetElementType,
  DynamicFieldDefinition,
  DATA_SETS,
  DYNAMIC_FIELDS,
  FieldInstanceMetadata,
  FieldLevelMetadata,
  IDENTIFIER,
  ISession,
  METADATA,
  QueryInput,
  QueryReason,
  RoutineDeterminationResult,
  RoutineValidationContext,
  RoutineValidationResult,
  TableComponent,
  ValidateReason
} from '@everestsystems/content-core';

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

export namespace question1Presentation {
  export type mode = 'main';

  export namespace dataSources {
    export namespace questionData {
      export type levels = {
        '': never;
        Header: {
          subtitle?: everest_appserver_primitive_Text | undefined | null;
          title?: everest_appserver_primitive_Text | undefined | null;
        };
        MigrationOptions: {
          hasExistingData?: everest_appserver_primitive_TrueFalse | undefined | null;
          hasExistingDataLabel?: everest_appserver_primitive_Text | undefined | null;
          isNewSetup?: everest_appserver_primitive_TrueFalse | undefined | null;
          isNewSetupLabel?: everest_appserver_primitive_Text | undefined | null;
          selectedValue?: everest_appserver_primitive_Text | undefined | null;
        };
        NavigationButtons: {
          backLabel?: everest_appserver_primitive_Text | undefined | null;
          canContinue?: everest_appserver_primitive_TrueFalse | undefined | null;
          canGoBack?: everest_appserver_primitive_TrueFalse | undefined | null;
          continueLabel?: everest_appserver_primitive_Text | undefined | null;
        };
        Progress: {
          currentStep?: everest_appserver_primitive_Number | undefined | null;
          progressPercentage?: everest_appserver_primitive_Decimal | undefined | null;
          stepLabel?: everest_appserver_primitive_Text | undefined | null;
          totalSteps?: everest_appserver_primitive_Number | undefined | null;
        };
        QuestionContent: {
          descriptions?: everest_appserver_primitive_Text | undefined | null;
          question?: everest_appserver_primitive_Text | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': never;
        Header: {
          subtitle?: FieldInstanceMetadata | undefined;
          title?: FieldInstanceMetadata | undefined;
        };
        MigrationOptions: {
          hasExistingData?: FieldInstanceMetadata | undefined;
          hasExistingDataLabel?: FieldInstanceMetadata | undefined;
          isNewSetup?: FieldInstanceMetadata | undefined;
          isNewSetupLabel?: FieldInstanceMetadata | undefined;
          selectedValue?: FieldInstanceMetadata | undefined;
        };
        NavigationButtons: {
          backLabel?: FieldInstanceMetadata | undefined;
          canContinue?: FieldInstanceMetadata | undefined;
          canGoBack?: FieldInstanceMetadata | undefined;
          continueLabel?: FieldInstanceMetadata | undefined;
        };
        Progress: {
          currentStep?: FieldInstanceMetadata | undefined;
          progressPercentage?: FieldInstanceMetadata | undefined;
          stepLabel?: FieldInstanceMetadata | undefined;
          totalSteps?: FieldInstanceMetadata | undefined;
        };
        QuestionContent: {
          descriptions?: FieldInstanceMetadata | undefined;
          question?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': never;
        Header: {
          subtitle?: FieldLevelMetadata | undefined;
          title?: FieldLevelMetadata | undefined;
        };
        MigrationOptions: {
          hasExistingData?: FieldLevelMetadata | undefined;
          hasExistingDataLabel?: FieldLevelMetadata | undefined;
          isNewSetup?: FieldLevelMetadata | undefined;
          isNewSetupLabel?: FieldLevelMetadata | undefined;
          selectedValue?: FieldLevelMetadata | undefined;
        };
        NavigationButtons: {
          backLabel?: FieldLevelMetadata | undefined;
          canContinue?: FieldLevelMetadata | undefined;
          canGoBack?: FieldLevelMetadata | undefined;
          continueLabel?: FieldLevelMetadata | undefined;
        };
        Progress: {
          currentStep?: FieldLevelMetadata | undefined;
          progressPercentage?: FieldLevelMetadata | undefined;
          stepLabel?: FieldLevelMetadata | undefined;
          totalSteps?: FieldLevelMetadata | undefined;
        };
        QuestionContent: {
          descriptions?: FieldLevelMetadata | undefined;
          question?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
        Header: never;
        MigrationOptions: never;
        NavigationButtons: never;
        Progress: never;
        QuestionContent: never;
      };

      export type parameters = never;

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = {
            Header?: levelMetadata['Header'];
            MigrationOptions?: levelMetadata['MigrationOptions'];
            NavigationButtons?: levelMetadata['NavigationButtons'];
            Progress?: levelMetadata['Progress'];
            QuestionContent?: levelMetadata['QuestionContent'];
          };

          export type queryConfigurations = {
            Header?: levelConfigurations['Header'];
            MigrationOptions?: levelConfigurations['MigrationOptions'];
            NavigationButtons?: levelConfigurations['NavigationButtons'];
            Progress?: levelConfigurations['Progress'];
            QuestionContent?: levelConfigurations['QuestionContent'];
          };

          export type queryData = {
            Header?: levels['Header'] & {
              [METADATA]?: instanceMetadata['Header'] | undefined;
            };
            MigrationOptions?: levels['MigrationOptions'] & {
              [METADATA]?: instanceMetadata['MigrationOptions'] | undefined;
            };
            NavigationButtons?: levels['NavigationButtons'] & {
              [METADATA]?: instanceMetadata['NavigationButtons'] | undefined;
            };
            Progress?: levels['Progress'] & {
              [METADATA]?: instanceMetadata['Progress'] | undefined;
            };
            QuestionContent?: levels['QuestionContent'] & {
              [METADATA]?: instanceMetadata['QuestionContent'] | undefined;
            };
          };

          export type input = {
            session: ISession;
            mode: mode;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
              Header?: {
                fields: ReadonlySet<keyof levels['Header']> | undefined;
              };
              MigrationOptions?: {
                fields: ReadonlySet<keyof levels['MigrationOptions']> | undefined;
              };
              NavigationButtons?: {
                fields: ReadonlySet<keyof levels['NavigationButtons']> | undefined;
              };
              Progress?: {
                fields: ReadonlySet<keyof levels['Progress']> | undefined;
              };
              QuestionContent?: {
                fields: ReadonlySet<keyof levels['QuestionContent']> | undefined;
              };
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }

        export namespace update_MigrationOptions {
          export type input = {
            session: ISession;
            mode: mode;
            fieldName: keyof levels['MigrationOptions'];
            oldFieldValue: unknown;
            newFieldValue: unknown;
          };

          export type output = void;
        }
      }

      export namespace routines {
        export namespace continueToNext {
          export type input = {
            selectedOption: everest_appserver_primitive_Text;
          };

          export type output = {
            currentStep: everest_appserver_primitive_Number;
            nextStep: everest_appserver_primitive_Text;
            success: everest_appserver_primitive_TrueFalse;
            validationMessage: everest_appserver_primitive_Text;
          };

          export type determineInput = {
            session: ISession;
            mode: mode;
            input: Pick<input, 'selectedOption'>;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
            input: input;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            input: input;
          };

          export type executeOutput = output;
        }

        export namespace goBack {
          export type input = never;

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
          };

          export type executeOutput = output;
        }

        export namespace initializeQuestion {
          export type input = never;

          export type output = {
            currentStep: everest_appserver_primitive_Number;
            questionData: everest_appserver_primitive_Text;
            totalSteps: everest_appserver_primitive_Number;
          };

          export type determineInput = {
            session: ISession;
            mode: mode;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
          };

          export type executeOutput = output;
        }

        export namespace selectOption {
          export type input = {
            optionValue: everest_appserver_primitive_Text;
          };

          export type output = {
            success: everest_appserver_primitive_TrueFalse;
          };

          export type determineInput = {
            session: ISession;
            mode: mode;
            input: Pick<input, 'optionValue'>;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
            input: input;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            input: input;
          };

          export type executeOutput = output;
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;

        update_MigrationOptions(input: callbacks.update_MigrationOptions.input): Promise<callbacks.update_MigrationOptions.output>;

        determine_continueToNext?(input: routines.continueToNext.determineInput): Promise<routines.continueToNext.determineOutput>;

        validate_continueToNext?(input: routines.continueToNext.validateInput): Promise<routines.continueToNext.validateOutput>;

        execute_continueToNext(input: routines.continueToNext.executeInput): Promise<routines.continueToNext.executeOutput>;

        determine_goBack?(input: routines.goBack.determineInput): Promise<routines.goBack.determineOutput>;

        validate_goBack?(input: routines.goBack.validateInput): Promise<routines.goBack.validateOutput>;

        execute_goBack(input: routines.goBack.executeInput): Promise<routines.goBack.executeOutput>;

        determine_initializeQuestion?(input: routines.initializeQuestion.determineInput): Promise<routines.initializeQuestion.determineOutput>;

        validate_initializeQuestion?(input: routines.initializeQuestion.validateInput): Promise<routines.initializeQuestion.validateOutput>;

        execute_initializeQuestion(input: routines.initializeQuestion.executeInput): Promise<routines.initializeQuestion.executeOutput>;

        determine_selectOption?(input: routines.selectOption.determineInput): Promise<routines.selectOption.determineOutput>;

        validate_selectOption?(input: routines.selectOption.validateInput): Promise<routines.selectOption.validateOutput>;

        execute_selectOption(input: routines.selectOption.executeInput): Promise<routines.selectOption.executeOutput>;
      }
    }
  }

  export type implementation = {
    questionData(): dataSources.questionData.implementation;
  };
}
