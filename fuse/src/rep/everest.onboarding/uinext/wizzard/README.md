# Onboarding Wizard Component

A dynamic, reusable onboarding wizard component that displays questions from the everest.onboarding database models with intelligent routing and local storage persistence.

## Features

- **Dynamic Question Loading**: Fetches questions and options from OnboardingQuestion and OnboardingQuestionOption models
- **Intelligent Routing**: Routes between questions based on user responses using the routing tree from q1.md
- **Local Storage Persistence**: Saves user progress and answers locally for session recovery
- **Progress Tracking**: Visual progress indicator showing completion percentage
- **Validation**: Built-in validation with error handling and user feedback
- **Responsive Design**: Mobile-friendly UI following Everest design system
- **Multiple Question Types**: Supports text input, single selection, multiple selection, and info display

## Architecture

### Files Structure
```
/uinext/wizzard/
├── onboardingWizard.ls              # Presentation layer definition
├── onboardingWizard.presentation.ts # Backend logic and data handling
├── onboardingWizard.ui.tsx          # React UI component
├── onboardingWizard.uitemplate.json # UI template configuration
├── q1.md                           # Question specifications and routing logic
└── README.md                       # This documentation
```

### Database Models Used
- **OnboardingQuestion**: Stores question text, title, and order
- **OnboardingQuestionOption**: Stores answer options for each question
- **OnboardingOptionToStepMapping**: Maps selected options to onboarding steps
- **OnboardingStep**: Defines follow-up actions based on user responses

## Usage

### Basic Implementation

```tsx
import OnboardingWizard from '@pkg/everest.onboarding/uinext/wizzard/onboardingWizard.ui';

function App() {
  return (
    <div>
      <OnboardingWizard />
    </div>
  );
}
```

### Presentation Hook Usage

```tsx
import { useTemplatePresentation } from '@pkg/everest.appserver/types/presentations/hooks/useTemplatePresentation';

const {
  data,
  actions,
  refresh,
  isLoading
} = useTemplatePresentation({
  urn: 'urn:evst:everest:onboarding:presentation:uinext/wizzard/onboardingWizard',
  mode: 'main',
});

// Initialize wizard
await actions.initializeWizard();

// Navigate to next question
const result = await actions.nextQuestion(selectedAnswers, currentQuestionUuid);

// Go back to previous question
await actions.previousQuestion();

// Save current answers
await actions.saveAnswer(questionUuid, answers);

// Submit completed wizard
const submitResult = await actions.submitWizard(allAnswers);
```

## Question Types

### Text Input Questions
```typescript
{
  questionType: 'text',
  validation: { required: true }
}
```
Renders a text area for user input.

### Single Selection Questions
```typescript
{
  questionType: 'single',
  options: {
    'option1': 'next-question-id',
    'option2': 'different-question-id'
  }
}
```
Renders radio buttons or checkboxes (single selection).

### Multiple Selection Questions
```typescript
{
  questionType: 'multiple'
}
```
Renders checkboxes allowing multiple selections.

### Information Display
```typescript
{
  questionType: 'info',
  nextQuestion: 'next-question-id'
}
```
Displays information with a continue button.

## Routing Logic

The wizard uses a routing tree defined in `q1.md` and implemented in the presentation layer:

```typescript
const QUESTION_ROUTING_TREE = {
  'q1-intro': {
    type: 'text',
    nextQuestion: 'q2-greeting',
    validation: { required: true }
  },
  'q3-existing-systems': {
    type: 'single',
    options: {
      'yes-migration': 'migration-planning-page',
      'no-new-setup': 'q4-currency'
    }
  }
  // ... more questions
};
```

## Local Storage

The component automatically saves:
- **Progress**: Current question index and UUID
- **Answers**: Individual question responses
- **User Data**: Name and session information

### Storage Keys
- `onboarding_wizard_progress`: Overall wizard state
- `onboarding_answers_{questionUuid}`: Individual question answers

## Validation

### Built-in Validation Rules
- **Required Questions**: Validates that required questions have answers
- **Format Validation**: Ensures answers are in correct format
- **Business Logic**: Custom validation based on question type

### Custom Validation
```typescript
private validateAnswers(answers: Record<string, any>, questionUuid: string): {
  isValid: boolean;
  errors: string[];
} {
  // Custom validation logic
}
```

## Styling

Uses Everest color scheme:
- **Morning Blue**: `#abbcd2`
- **Midnight Blue**: `#2e3948`
- **Amaranth**: `#f96773`
- **Sky Blue**: `#3b7dc7`

### Component Styling
- Responsive design with Tailwind CSS
- Ant Design components for consistency
- Custom styling for Everest branding

## API Integration

### Database Operations
```typescript
// Load questions
const questionClient = OnboardingQuestion.client(this.env);
const questions = await questionClient.query({
  orderBy: [{ field: 'orderIndex', direction: 'asc' }]
});

// Load options
const optionClient = OnboardingQuestionOption.client(this.env);
const options = await optionClient.query({
  where: { onboardingQuestionUUID: questionUuid }
});

// Process completed answers
const stepClient = OnboardingStep.client(this.env);
await stepClient.update(stepUuid, { status: 'NotStarted' });
```

## Error Handling

### Common Error Scenarios
- Database connection issues
- Invalid question routing
- Local storage access problems
- Validation failures

### Error Recovery
- Graceful degradation for storage issues
- Retry mechanisms for network failures
- User-friendly error messages
- Fallback routing options

## Testing

### Unit Tests
Test individual methods and validation logic:
```typescript
describe('OnboardingWizard', () => {
  test('validates required questions', () => {
    // Test validation logic
  });
  
  test('routes to correct next question', () => {
    // Test routing logic
  });
});
```

### Integration Tests
Test full wizard flow with database integration.

## Performance Considerations

- **Lazy Loading**: Questions loaded on-demand
- **Caching**: Local storage for offline capability
- **Debounced Saves**: Prevent excessive localStorage writes
- **Optimistic Updates**: Immediate UI feedback

## Customization

### Adding New Question Types
1. Update `QUESTION_ROUTING_TREE`
2. Add rendering logic in `renderQuestionContent()`
3. Update validation in `validateAnswers()`

### Custom Styling
Override CSS classes or modify the color scheme in the component.

### Extended Routing
Add complex routing logic by extending the `determineNextQuestion()` method.

## Deployment

1. Ensure database models are properly migrated
2. Configure presentation URN in routing
3. Test with sample question data
4. Deploy UI component to target environment

## Troubleshooting

### Common Issues
- **Questions not loading**: Check database connection and model permissions
- **Routing not working**: Verify routing tree configuration
- **Local storage errors**: Check browser storage limits and permissions
- **Validation failures**: Review validation rules and question requirements

### Debug Mode
Enable debug logging by setting localStorage item:
```javascript
localStorage.setItem('onboarding_debug', 'true');
```

## Future Enhancements

- Multi-language support
- Advanced analytics and tracking
- Custom question types
- Integration with external systems
- Automated testing framework
- Performance monitoring
