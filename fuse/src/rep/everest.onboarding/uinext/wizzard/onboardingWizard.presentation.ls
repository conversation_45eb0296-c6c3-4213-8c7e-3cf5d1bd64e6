package everest.onboarding

template presentation onboardingWizard {

  mode main {
    on wizard allow view, change
    on currentQuestion allow view
    on questionOptions allow view, change
    on progressIndicator allow view
    on navigation allow view
    allow actions initializeWizard, nextQuestion, previousQuestion, saveAnswer, submitWizard, resetWizard
  }

  state {
    currentQuestionIndex: Number<Int>
    totalQuestions: Number<Int>
    currentQuestionUuid: UUID
    userName: Text
    wizardCompleted: TrueFalse
    validationErrors: Array<Text>
    isLoading: TrueFalse
  }

  object data-source wizard {
    shape {
      child CurrentQuestion {
        uuid: field<OnboardingQuestion.uuid>
        text: field<OnboardingQuestion.text>
        title: field<OnboardingQuestion.title>
        orderIndex: field<OnboardingQuestion.orderIndex>
        questionType: Text // 'single', 'multiple', 'text', 'info'
        isRequired: TrueFalse
        validationMessage: Text
      }

      children QuestionOptions {
        uuid: field<OnboardingQuestionOption.uuid>
        description: field<OnboardingQuestionOption.description>
        value: field<OnboardingQuestionOption.value>
        orderIndex: field<OnboardingQuestionOption.orderIndex>
        isSelected: field<OnboardingQuestionOption.isSelected>
        nextQuestionUuid: UUID // For routing logic
      }

      child ProgressIndicator {
        currentStep: Number<Int>
        totalSteps: Number<Int>
        completedSteps: Array<Number<Int>>
        progressPercentage: Number<Decimal>
      }

      child Navigation {
        canGoBack: TrueFalse
        canGoNext: TrueFalse
        showSubmit: TrueFalse
        nextButtonText: Text
        backButtonText: Text
      }

      child UserData {
        userName: Text
        answers: JSON // Store all user answers
        currentAnswers: JSON // Current question answers
      }
    }

    modifications {
      on QuestionOptions support update
      on UserData support update
    }

    routine initializeWizard {
      properties {
        side-effects true
        affected-data-sets wizard
      }
    }

    routine nextQuestion {
      inputs {
        selectedAnswers: JSON
        currentQuestionUuid: UUID
      }
      outputs {
        nextQuestionUuid: UUID
        isCompleted: TrueFalse
      }
      properties {
        side-effects true
        affected-data-sets wizard
      }
    }

    routine previousQuestion {
      properties {
        side-effects true
        affected-data-sets wizard
      }
    }

    routine saveAnswer {
      inputs {
        questionUuid: UUID
        answers: JSON
      }
      properties {
        side-effects false
      }
    }

    routine submitWizard {
      inputs {
        allAnswers: JSON
      }
      outputs {
        success: TrueFalse
        redirectUrl: Text
      }
      properties {
        side-effects true
      }
    }

    routine resetWizard {
      properties {
        side-effects true
        affected-data-sets wizard
      }
    }
  }

  struct currentQuestion {
    data wizard.CurrentQuestion
    fields *
  }

  data-set questionOptions {
    component FieldGroup {
      variant = "checkbox-group"
    }
    data wizard.QuestionOptions
    fields uuid, description, value, isSelected, orderIndex
  }

  struct progressIndicator {
    data wizard.ProgressIndicator
    fields *
  }

  struct navigation {
    data wizard.Navigation
    fields *
  }

  struct userData {
    data wizard.UserData
    fields *
  }

  delegate action initializeWizard to data-source<wizard>.initializeWizard

  delegate action nextQuestion to data-source<wizard>.nextQuestion

  delegate action previousQuestion to data-source<wizard>.previousQuestion

  delegate action saveAnswer to data-source<wizard>.saveAnswer

  delegate action submitWizard to data-source<wizard>.submitWizard

  delegate action resetWizard to data-source<wizard>.resetWizard
}
